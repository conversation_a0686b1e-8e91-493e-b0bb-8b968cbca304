import React, { useState, useEffect, useCallback, useRef } from "react";

import { Send, Bot, User, Loader2 } from "lucide-react";

import { toast } from "sonner";

import { useAppSelector, useAppDispatch } from "../app/hooks";
import { useWebSocketContext } from "../contexts/WebSocketContext";
import { ChatMessage, Document } from "../types/chat";
import {
  selectMessages,
  addMessage,
  sendMessage,
  selectIsLoadingMessages,
  selectIsSendingMessage,
} from "../features/chat/chatSlice";

import MarkdownRenderer from "./MarkdownRenderer";

import { Button } from "./ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "./ui/card";
import { Input } from "./ui/input";

interface Message {
  id: string;
  type: "user" | "assistant";
  content: string;
  timestamp: string;
  metadata?: Record<string, unknown>;
}

interface ChatWithStreamingProps {
  sessionId: string;
  documents?: Document[];
  className?: string;
}

const ChatWithStreaming: React.FC<ChatWithStreamingProps> = ({
  sessionId,
  documents = [],
  className = "",
}) => {
  const [inputMessage, setInputMessage] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [streamingMessage, setStreamingMessage] = useState("");
  const [isStreaming, setIsStreaming] = useState(false);
  const [hasJoinedRoom, setHasJoinedRoom] = useState(false);
  const [streamingStartTime, setStreamingStartTime] = useState<number | null>(null);

  const webSocket = useWebSocketContext();
  const dispatch = useAppDispatch();

  // Get messages from Redux store
  const reduxMessages = useAppSelector(selectMessages);
  const isLoadingHistory = useAppSelector(selectIsLoadingMessages);
  const isSendingMessage = useAppSelector(selectIsSendingMessage);

  // Create refs after variables are declared
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const prevSessionIdRef = useRef<string | null>(null);
  const isStreamingRef = useRef(isStreaming);
  const reduxMessagesRef = useRef(reduxMessages);
  const dispatchRef = useRef(dispatch);
  const webSocketRef = useRef(webSocket);

  // Convert Redux messages to component format
  const messages: Message[] = reduxMessages.map((msg) => ({
    id: msg.id,
    type: msg.role === "user" ? "user" : "assistant",
    content: msg.content,
    timestamp: msg.timestamp,
    metadata: msg.metadata || {},
  }));

  // Update refs when values change
  useEffect(() => {
    isStreamingRef.current = isStreaming;
  }, [isStreaming]);

  useEffect(() => {
    reduxMessagesRef.current = reduxMessages;
  }, [reduxMessages]);

  useEffect(() => {
    dispatchRef.current = dispatch;
  }, [dispatch]);

  useEffect(() => {
    webSocketRef.current = webSocket;
  }, [webSocket]);

  // Handle session changes and cleanup
  useEffect(() => {
    if (!sessionId) return;

    // Reset join state when sessionId changes
    if (prevSessionIdRef.current !== sessionId) {
      // Leave previous room if it exists
      if (prevSessionIdRef.current) {
        console.log(`Leaving previous chat room: ${prevSessionIdRef.current}`);
        webSocket.leaveChatRoom(prevSessionIdRef.current);
      }
      setHasJoinedRoom(false);
      prevSessionIdRef.current = sessionId;
    }

    // Clear any existing streaming data for this session
    webSocket.clearChatStream(sessionId);
    setStreamingMessage("");
    setIsStreaming(false);
    setIsLoading(false);
    setStreamingStartTime(null);

    // Cleanup function to leave room when component unmounts or sessionId changes
    return () => {
      if (sessionId && prevSessionIdRef.current === sessionId) {
        console.log(`Cleanup: Leaving chat room: ${sessionId}`);
        webSocket.leaveChatRoom(sessionId);
        setHasJoinedRoom(false);
      }
    };
  }, [sessionId, webSocket]);

  // Join chat room after loading history (with duplicate prevention)
  useEffect(() => {
    if (
      sessionId &&
      webSocket.isConnected &&
      !isLoadingHistory &&
      !hasJoinedRoom
    ) {
      console.log(`Attempting to join chat room: ${sessionId}`);
      webSocket.joinChatRoom(sessionId);
      setHasJoinedRoom(true);
    } else if (!webSocket.isConnected && hasJoinedRoom) {
      // Reset join state if WebSocket disconnects
      console.log(`WebSocket disconnected, resetting join state for session: ${sessionId}`);
      setHasJoinedRoom(false);
    }
  }, [
    sessionId,
    webSocket,
    isLoadingHistory,
    hasJoinedRoom,
  ]);

  // Monitor chat streams for new messages only
  useEffect(() => {
    if (!sessionId || isLoadingHistory) return;

    const checkForStreams = () => {
      const chatStream = webSocketRef.current.getChatStream(sessionId);
      if (chatStream) {
        // Update streaming message with current text
        setStreamingMessage(prev => {
          // Only update if text actually changed to prevent unnecessary re-renders
          if (prev !== chatStream.text) {
            console.log(`Streaming update for session ${sessionId}:`, {
              textLength: chatStream.text.length,
              isComplete: chatStream.isComplete,
              preview: chatStream.text.slice(-50) // Last 50 characters
            });
            return chatStream.text;
          }
          return prev;
        });

        setIsStreaming(!chatStream.isComplete);

        // Set streaming start time if not already set
        setStreamingStartTime(prev => {
          if (!prev && chatStream.text && !chatStream.isComplete) {
            return Date.now();
          }
          return prev;
        });

        if (chatStream.isComplete && chatStream.text.trim()) {
          // Use ref to get current messages to avoid dependency
          const currentMessages = reduxMessagesRef.current;
          const messageExists = currentMessages.some(
            (msg) => msg.content === chatStream.text && msg.role === "assistant"
          );

          if (!messageExists) {
            console.log(`Adding completed streaming message to Redux. Length: ${chatStream.text.length}`);
            // Add completed streaming message to Redux
            const newMessage: ChatMessage = {
              id: `assistant-${Date.now()}-${Math.random()
                .toString(36)
                .substring(2, 11)}`,
              role: "assistant",
              content: chatStream.text,
              timestamp: new Date().toISOString(),
              metadata: chatStream.metadata || {},
            };
            dispatchRef.current(addMessage(newMessage));
          } else {
            console.log('Streaming message already exists in Redux, skipping duplicate');
          }

          // Clear streaming state
          setStreamingMessage("");
          setIsStreaming(false);
          setIsLoading(false);
          setStreamingStartTime(null);

          // Clear the stream from WebSocket context
          webSocketRef.current.clearChatStream(sessionId);
        }
      } else {
        // Check for streaming timeout (30 seconds) only if we're actually streaming
        setStreamingStartTime(prev => {
          if (prev && isStreamingRef.current) {
            const streamingDuration = Date.now() - prev;
            if (streamingDuration > 30000) {
              console.warn(`Streaming timeout for session ${sessionId} after ${streamingDuration}ms`);
              setIsStreaming(false);
              setIsLoading(false);
              setStreamingMessage("");
              return null;
            }
          }
          return prev;
        });
      }
    };

    const interval = setInterval(checkForStreams, 100);
    return () => clearInterval(interval);
  }, [sessionId, isLoadingHistory]); // Minimal dependencies to prevent constant re-renders

  // Auto-scroll to bottom only when messages change or streaming updates
  useEffect(() => {
    // Use a small delay to ensure DOM is updated
    const timeoutId = setTimeout(() => {
      messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
    }, 50);

    return () => clearTimeout(timeoutId);
  }, [messages.length, streamingMessage]); // Only scroll when message count changes or streaming updates

  const sendMessageHandler = useCallback(async () => {
    if (!inputMessage.trim() || isLoading || isSendingMessage || !sessionId)
      return;

    const userMessage: ChatMessage = {
      id: `user-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`,
      role: "user",
      content: inputMessage.trim(),
      timestamp: new Date().toISOString(),
    };

    // Add user message to Redux immediately
    dispatch(addMessage(userMessage));
    setInputMessage("");
    setIsLoading(true);
    setIsStreaming(true);
    setStreamingStartTime(null); // Reset streaming timer

    // Clear any existing streaming data for this session before sending new message
    webSocket.clearChatStream(sessionId);

    try {
      // Send message via Redux action with chat history
      const result = await dispatch(
        sendMessage({
          sessionId,
          message: userMessage.content,
          chatHistory: reduxMessages, // Pass original Redux messages with 'role' field
          useAgent: false,
        })
      ).unwrap();

      console.log("Message sent successfully:", result);

      // Handle response - check if we got a direct response or need to wait for WebSocket
      if (result && (result.response || result.message || result.data?.message)) {
        // We got a direct response, add it immediately regardless of WebSocket status
        console.log("Got direct response, adding message immediately");
        const assistantMessage: ChatMessage = {
          id: `assistant-${Date.now()}-${Math.random()
            .toString(36)
            .substring(2, 11)}`,
          role: "assistant",
          content: result.response || result.message || result.data?.message || "",
          timestamp: new Date().toISOString(),
          metadata: result.metadata || result.data?.metadata || {},
        };
        dispatch(addMessage(assistantMessage));
        setIsLoading(false);
        setIsStreaming(false);
        setStreamingStartTime(null);
      } else if (webSocket.isConnected) {
        // No direct response but WebSocket is connected, wait for streaming
        console.log("WebSocket connected, waiting for streaming response...");

        // Add a safety timeout to reset loading state if WebSocket doesn't respond
        setTimeout(() => {
          console.log("WebSocket timeout - resetting loading state");
          setIsLoading(false);
          setIsStreaming(false);
          setStreamingStartTime(null);
        }, 10000); // 10 second timeout
      } else {
        // No response and no WebSocket connection
        console.log("No response received and WebSocket not connected");
        setIsLoading(false);
        setIsStreaming(false);
        setStreamingStartTime(null);
        toast.error("No response received from server");
      }
    } catch (error) {
      console.error("Error sending message:", error);
      const errorMessage: ChatMessage = {
        id: `error-${Date.now()}-${Math.random()
          .toString(36)
          .substring(2, 11)}`,
        role: "assistant",
        content: `Error: ${(error as Error).message}`,
        timestamp: new Date().toISOString(),
      };
      dispatch(addMessage(errorMessage));
      setIsLoading(false);
      setIsStreaming(false);
      setStreamingStartTime(null);
      toast.error("Failed to send message");
    }
  }, [
    inputMessage,
    isLoading,
    isSendingMessage,
    sessionId,
    dispatch,
    reduxMessages,
    webSocket,
  ]);

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault();
      sendMessageHandler();
    }
  };

  const formatTimestamp = (timestamp: string) => {
    return new Date(timestamp).toLocaleTimeString([], {
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  // Render chat UI with streaming message display
  if (isLoadingHistory) {
    return (
      <Card className={`flex flex-col h-full ${className}`}>
        <CardHeader className="flex-shrink-0">
          <CardTitle>Chat Session</CardTitle>
        </CardHeader>
        <CardContent className="flex-1 flex items-center justify-center">
          <div className="flex flex-col items-center space-y-2">
            <Loader2 className="h-8 w-8 animate-spin text-blue-600" />
            <p className="text-sm text-gray-600">Loading chat history...</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className={`flex flex-col h-full ${className}`}>
      {/* Messages Area */}
      <div className="flex-1 overflow-y-auto p-4" style={{ minHeight: 0 }}>
        <div className="space-y-4">
          {messages.map((message) => (
            <div
              key={message.id}
              className={`flex items-start space-x-3 ${
                message.type === "user" ? "justify-end" : "justify-start"
              }`}
            >
              {message.type === "assistant" && (
                <div className="flex-shrink-0 w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                  <Bot className="h-4 w-4 text-blue-600" />
                </div>
              )}

              <div
                className={`max-w-xs lg:max-w-md px-4 py-2 rounded-lg ${
                  message.type === "user"
                    ? "bg-blue-600 text-white"
                    : "bg-gray-100 text-gray-900"
                }`}
              >
                {message.type === "user" ? (
                  <p className="text-sm whitespace-pre-wrap">
                    {message.content}
                  </p>
                ) : (
                  <MarkdownRenderer
                    content={message.content}
                    className="text-sm"
                  />
                )}
                <p
                  className={`text-xs mt-1 ${
                    message.type === "user"
                      ? "text-blue-100"
                      : "text-gray-500"
                  }`}
                >
                  {formatTimestamp(message.timestamp)}
                </p>
              </div>

              {message.type === "user" && (
                <div className="flex-shrink-0 w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center">
                  <User className="h-4 w-4 text-gray-600" />
                </div>
              )}
            </div>
          ))}

          {/* Streaming Message */}
          {isStreaming && streamingMessage && (
            <div className="flex items-start space-x-3 justify-start">
              <div className="flex-shrink-0 w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                <Bot className="h-4 w-4 text-blue-600" />
              </div>
              <div className="max-w-xs lg:max-w-md px-4 py-2 rounded-lg bg-gray-100 text-gray-900">
                <MarkdownRenderer
                  content={streamingMessage}
                  className="text-sm"
                />
                <div className="flex items-center space-x-1 mt-1">
                  <Loader2 className="h-3 w-3 animate-spin text-gray-500" />
                  <p className="text-xs text-gray-500">Typing...</p>
                </div>
              </div>
            </div>
          )}

          {/* Loading indicator when no streaming */}
          {isLoading && !isStreaming && (
            <div className="flex items-start space-x-3 justify-start">
              <div className="flex-shrink-0 w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                <Loader2 className="h-4 w-4 animate-spin text-blue-600" />
              </div>
              <div className="max-w-xs lg:max-w-md px-4 py-2 rounded-lg bg-gray-100 text-gray-900">
                <p className="text-sm text-gray-500">Thinking...</p>
              </div>
            </div>
          )}

          <div ref={messagesEndRef} />
        </div>
      </div>

      {/* Input Area */}
      <div className="border-t p-4 flex-shrink-0 bg-white">
        <div className="flex space-x-2">
          <Input
            value={inputMessage}
            onChange={(e) => setInputMessage(e.target.value)}
            onKeyDown={handleKeyPress}
            placeholder="Type your message..."
            disabled={isLoading || isSendingMessage}
            className="flex-1"
          />
          <Button
            onClick={sendMessageHandler}
            disabled={isLoading || isSendingMessage || !inputMessage.trim()}
            size="icon"
          >
            {isLoading || isSendingMessage ? (
              <Loader2 className="h-4 w-4 animate-spin" />
            ) : (
              <Send className="h-4 w-4" />
            )}
          </Button>
        </div>
      </div>
    </div>
  );
};

export default ChatWithStreaming;
